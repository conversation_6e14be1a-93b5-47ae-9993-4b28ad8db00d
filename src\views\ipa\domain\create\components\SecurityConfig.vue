<template>
    <div class="security-config">
        <cute-titled-block title="DDoS防护">
            <template #content>
                <div class="ddos-wrapper">
                    <div v-for="(item, key) in functionData" :key="key" class="common-item">
                        <i class="icon-style" :style="iconStyle(item)" />
                        <div class="item-content">
                            <div class="content-operation">
                                <span class="content-style-label">{{ item.label }}</span>
                                <div class="switch-box">
                                    <el-switch
                                        v-model="item.enabled"
                                        :disabled="item.disabled"
                                        @change="handleSwitchChange(item, $event)"
                                    />
                                </div>
                            </div>
                            <span>
                                {{ item.content }}
                            </span>
                        </div>
                    </div>
                </div>
            </template>
        </cute-titled-block>
    </div>
</template>

<script>
export default {
    name: "SecurityConfig",
    props: {},
    data() {
        return {
            // DDoS防护功能数据
            functionData: [
                {
                    id: "ddos_mainland",
                    label: "DDoS防护-中国内地",
                    content: "订购中国内地全量套餐，可配置启用对中国内地区域的DDoS防护。",
                    icon: "bot-bg-1.png",
                    enabled: false,
                    disabled: false,
                },
                {
                    id: "ddos_global",
                    label: "DDoS防护-全球（不含中国内地）",
                    content:
                        "订购全球（不含中国内地）或全球套餐，可配置启用对全球（不含中国内地）区域的DDoS防护。",
                    icon: "bot-bg-1.png",
                    enabled: true,
                    disabled: false,
                },
            ],
        };
    },
    methods: {
        /**
         * 处理开关状态变化
         * @param {Object} item - 功能项数据
         * @param {Boolean} value - 开关状态
         */
        handleSwitchChange(item, value) {
            console.log(`${item.label} 开关状态变更为: ${value}`);
            // 这里可以添加具体的业务逻辑
            this.$emit("ddos-change", {
                id: item.id,
                enabled: value,
                label: item.label,
            });
        },

        /**
         * 获取表单数据 - 供父组件调用
         * @returns {Object} 表单数据
         */
        getFormData() {
            return {
                ddosConfig: this.functionData.map(item => ({
                    id: item.id,
                    enabled: item.enabled,
                })),
            };
        },

        /**
         * 表单验证方法 - 供父组件调用
         * @returns {Object} 验证结果
         */
        async validateForm() {
            return {
                valid: true,
                data: this.getFormData(),
            };
        },
        iconStyle(item) {
            const img = item.icon;
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const imgSrc = require("../images/" + img);
            return {
                "background-image": `url(${imgSrc})`,
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.security-config {
    .ddos-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }
    // 功能项容器样式
    .common-item {
        display: flex;
        padding: 20px;
        margin-bottom: 16px;
        background: #ffffff;
        border-radius: 3px;
        width: calc(50% - 10px);
        box-shadow: 0 2px 8px 0 rgba(200, 201, 204, 0.5);
        max-width: 400px;
    }

    // 标签行样式 - 包含图标和标题
    .flex-row-style {
        display: flex;
        align-items: center;
    }

    .icon-style {
        width: 80px;
        height: 80px;
        margin-right: 12px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        flex-shrink: 0;

        // 默认图标样式 - 使用蓝色背景的圆形图标
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%234A90E2'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M12 6v6l4 2' stroke='white' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
    }

    .content-style-label {
        font-size: 14px;
    }

    .item-content {
        padding-top: 12px;
    }

    .content-operation {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    // 开关容器样式
    .switch-box {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    // 响应式设计
    @media (max-width: 768px) {
        .common-item {
            padding: 16px;
        }

        .content-style {
            padding-left: 0;
            margin-top: 8px;
        }

        .switch-box {
            justify-content: flex-start;
            margin-top: 12px;
        }
    }
}
</style>
