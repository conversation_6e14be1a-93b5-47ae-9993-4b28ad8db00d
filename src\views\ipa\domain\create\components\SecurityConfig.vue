<template>
    <div class="security-config">
        <cute-titled-block title="DDoS防护">
            <template #content>
                <div class="security-config__wrapper">
                    <div
                        v-for="(item, key) in functionData"
                        :key="key"
                        class="security-config__item"
                        :class="{ 'security-config__item--disabled': disabled }"
                    >
                        <i class="security-config__icon" :style="iconStyle(item)" />
                        <div class="security-config__content">
                            <div class="security-config__header">
                                <span class="security-config__label">{{ item.label }}</span>
                                <div class="security-config__switch">
                                    <el-switch
                                        v-model="item.enabled"
                                        :disabled="item.disabled || disabled"
                                        @change="handleSwitchChange(item, $event)"
                                    />
                                </div>
                            </div>
                            <span class="security-config__description">
                                {{ item.content }}
                            </span>
                        </div>
                        <!-- 禁用状态蒙层 -->
                        <div v-if="disabled" class="security-config__overlay">
                            <span class="security-config__overlay-text">服务区域不匹配</span>
                        </div>
                    </div>
                </div>
            </template>
        </cute-titled-block>
    </div>
</template>

<script>
export default {
    name: "SecurityConfig",
    props: {
        /**
         * 是否禁用整个组件
         */
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            // DDoS防护功能数据
            functionData: [
                {
                    id: "ddos_mainland",
                    label: "DDoS防护-中国内地",
                    content: "订购中国内地全量套餐，可配置启用对中国内地区域的DDoS防护。",
                    icon: "bot-bg-1.png",
                    enabled: false,
                    disabled: false,
                },
                {
                    id: "ddos_global",
                    label: "DDoS防护-全球（不含中国内地）",
                    content:
                        "订购全球（不含中国内地）或全球套餐，可配置启用对全球（不含中国内地）区域的DDoS防护。",
                    icon: "bot-bg-1.png",
                    enabled: true,
                    disabled: false,
                },
            ],
        };
    },
    methods: {
        /**
         * 处理开关状态变化
         * @param {Object} item - 功能项数据
         * @param {Boolean} value - 开关状态
         */
        handleSwitchChange(item, value) {
            console.log(`${item.label} 开关状态变更为: ${value}`);
            // 这里可以添加具体的业务逻辑
            this.$emit("ddos-change", {
                id: item.id,
                enabled: value,
                label: item.label,
            });
        },

        /**
         * 获取表单数据 - 供父组件调用
         * @returns {Object} 表单数据
         */
        getFormData() {
            return {
                ddosConfig: this.functionData.map(item => ({
                    id: item.id,
                    enabled: item.enabled,
                })),
            };
        },

        /**
         * 表单验证方法 - 供父组件调用
         * @returns {Object} 验证结果
         */
        async validateForm() {
            return {
                valid: true,
                data: this.getFormData(),
            };
        },
        iconStyle(item) {
            const img = item.icon;
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const imgSrc = require("../images/" + img);
            return {
                "background-image": `url(${imgSrc})`,
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.security-config {
    // 主容器包装器
    &__wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }

    // 功能项卡片
    &__item {
        position: relative;
        display: flex;
        padding: 20px;
        margin-bottom: 16px;
        background: #ffffff;
        border-radius: 3px;
        width: calc(50% - 10px);
        box-shadow: 0 2px 8px 0 rgba(200, 201, 204, 0.5);
        max-width: 400px;
        transition: all 0.3s ease;

        // 禁用状态修饰符
        &--disabled {
            opacity: 0.6;
        }
    }

    // 功能项图标
    &__icon {
        width: 80px;
        height: 80px;
        margin-right: 12px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        flex-shrink: 0;
        // 默认图标样式 - 使用蓝色背景的圆形图标
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%234A90E2'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M12 6v6l4 2' stroke='white' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
    }

    // 功能项内容区域
    &__content {
        flex: 1;
        padding-top: 12px;
    }

    // 功能项头部（标题和开关）
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
    }

    // 功能项标题
    &__label {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 1.4;
    }

    // 功能项描述
    &__description {
        font-size: 12px;
        color: #666666;
        line-height: 1.5;
    }

    // 开关容器
    &__switch {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex-shrink: 0;
        margin-left: 12px;
    }

    // 禁用状态蒙层
    &__overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    // 蒙层文案
    &__overlay-text {
        font-size: 14px;
        font-weight: 500;
        color: #999999;
        text-align: center;
        padding: 8px 16px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // 响应式设计
    @media (max-width: 768px) {
        &__item {
            width: 100%;
            padding: 16px;
        }

        &__header {
            flex-direction: column;
            align-items: flex-start;
        }

        &__switch {
            justify-content: flex-start;
            margin-left: 0;
            margin-top: 12px;
        }

        &__icon {
            width: 60px;
            height: 60px;
        }
    }
}
</style>
